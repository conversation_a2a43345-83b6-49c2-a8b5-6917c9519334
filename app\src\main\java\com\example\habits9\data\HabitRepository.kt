package com.example.habits9.data

import android.util.Log
import com.example.habits9.data.firestore.FirestoreConverters
import com.example.habits9.data.firestore.FirestoreHabit
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.ListenerRegistration
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class HabitRepository @Inject constructor(
    private val firestore: FirebaseFirestore,
    private val auth: FirebaseAuth
) {

    companion object {
        private const val TAG = "HabitRepository"
        private const val USERS_COLLECTION = "users"
        private const val HABITS_COLLECTION = "habits"
    }

    /**
     * Gets all habits for the current user using real-time listeners.
     * Returns an empty flow if user is not authenticated.
     */
    fun getAllHabits(): Flow<List<Habit>> = callbackFlow {
        val userId = auth.currentUser?.uid

        // Handle authentication gracefully without early return
        val listener: ListenerRegistration? = if (userId != null) {
            firestore
                .collection(USERS_COLLECTION)
                .document(userId)
                .collection(HABITS_COLLECTION)
                // Ensure a stable order from Firestore so CUSTOM_ORDER reflects immediately
                .orderBy("customOrderIndex")
                .addSnapshotListener { snapshot, error ->
                    if (error != null) {
                        Log.e(TAG, "Error listening to habits", error)
                        close(error)
                        return@addSnapshotListener
                    }

                    if (snapshot != null) {
                        val habits = snapshot.documents.mapNotNull { document ->
                            try {
                                val firestoreHabit = document.toObject(FirestoreHabit::class.java)
                                firestoreHabit?.let {
                                    // Use document ID as the Firestore ID
                                    val habitWithId = it.copy(id = document.id)
                                    FirestoreConverters.firestoreToHabit(habitWithId)
                                }
                            } catch (e: Exception) {
                                Log.e(TAG, "Error converting document to habit: ${document.id}", e)
                                null
                            }
                        }
                        trySend(habits)
                    } else {
                        trySend(emptyList())
                    }
                }
        } else {
            Log.w(TAG, "User not authenticated, returning empty habits list")
            trySend(emptyList())
            null
        }

        awaitClose { listener?.remove() }
    }

    /**
     * Gets all habits for the current user synchronously (one-time read).
     * This is used for force refresh operations where we need immediate access to latest data.
     */
    suspend fun getAllHabitsOnce(): List<Habit> {
        val userId = auth.currentUser?.uid
        if (userId == null) {
            Log.w(TAG, "User not authenticated, returning empty habits list")
            return emptyList()
        }

        return try {
            val snapshot = firestore
                .collection(USERS_COLLECTION)
                .document(userId)
                .collection(HABITS_COLLECTION)
                .orderBy("customOrderIndex")
                .get()
                .await()

            snapshot.documents.mapNotNull { document ->
                try {
                    val firestoreHabit = document.toObject(FirestoreHabit::class.java)
                    firestoreHabit?.let {
                        val habitWithId = it.copy(id = document.id)
                        FirestoreConverters.firestoreToHabit(habitWithId)
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error converting document to habit: ${document.id}", e)
                    null
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting habits synchronously", e)
            emptyList()
        }
    }

    /**
     * Gets a specific habit by ID using real-time listener.
     * FIXED: Now listens to all habits and filters by hash-based ID
     */
    fun getHabitById(habitId: Long): Flow<Habit> = callbackFlow {
        Log.d(TAG, "getHabitById called with habitId: $habitId")
        val userId = auth.currentUser?.uid

        // Handle authentication gracefully without early return
        val listener: ListenerRegistration? = if (userId != null) {
            Log.d(TAG, "User authenticated: $userId")

            // FIXED: Listen to all habits and filter by the hash-based ID
            firestore
                .collection(USERS_COLLECTION)
                .document(userId)
                .collection(HABITS_COLLECTION)
                .addSnapshotListener { snapshot, error ->
                    if (error != null) {
                        Log.e(TAG, "Error listening to habits for ID $habitId", error)
                        close(error)
                        return@addSnapshotListener
                    }

                    if (snapshot != null) {
                        Log.d(TAG, "Found ${snapshot.documents.size} habit documents")
                        // Use functional approach to avoid break in lambda and smart cast issues
                        val foundHabit = snapshot.documents.firstNotNullOfOrNull { document ->
                            try {
                                val firestoreHabit = document.toObject(FirestoreHabit::class.java)
                                firestoreHabit?.let {
                                    val habitWithId = it.copy(id = document.id)
                                    val habit = FirestoreConverters.firestoreToHabit(habitWithId)
                                    Log.d(TAG, "Checking habit: ${habit.name} with id: ${habit.id} against target: $habitId")
                                    if (habit.id == habitId) habit else null
                                }
                            } catch (e: Exception) {
                                Log.e(TAG, "Error converting habit document: ${document.id}", e)
                                null
                            }
                        }

                        if (foundHabit != null) {
                            Log.d(TAG, "Found habit: ${foundHabit.name}")
                            trySend(foundHabit)
                        } else {
                            Log.w(TAG, "Habit with id $habitId not found among ${snapshot.documents.size} documents")
                            close(NoSuchElementException("Habit not found"))
                        }
                    } else {
                        close(NoSuchElementException("Habit not found"))
                    }
                }
        } else {
            Log.w(TAG, "User not authenticated")
            close(IllegalStateException("User not authenticated"))
            null
        }

        awaitClose { listener?.remove() }
    }

    /**
     * Gets a specific habit by ID synchronously (one-time read).
     * FIXED: Now searches for habit by hash-based ID instead of using it as document ID
     */
    suspend fun getHabitByIdSync(habitId: Long): Habit? {
        val userId = auth.currentUser?.uid
        if (userId == null) {
            Log.w(TAG, "User not authenticated")
            return null
        }

        return try {
            // FIXED: Query all habits to find the one with matching hash-based ID
            val snapshot = firestore
                .collection(USERS_COLLECTION)
                .document(userId)
                .collection(HABITS_COLLECTION)
                .get()
                .await()

            for (document in snapshot.documents) {
                val firestoreHabit = document.toObject(FirestoreHabit::class.java)
                firestoreHabit?.let {
                    val habitWithId = it.copy(id = document.id)
                    val habit = FirestoreConverters.firestoreToHabit(habitWithId)
                    if (habit.id == habitId) {
                        return habit
                    }
                }
            }

            null // Habit not found
        } catch (e: Exception) {
            Log.e(TAG, "Error getting habit $habitId", e)
            null
        }
    }

    /**
     * Inserts a new habit for the current user.
     */
    suspend fun insertHabit(habit: Habit) {
        val userId = auth.currentUser?.uid
        if (userId == null) {
            Log.w(TAG, "User not authenticated, cannot insert habit")
            throw IllegalStateException("User not authenticated")
        }

        try {
            // Convert habit to a Map to ensure proper Firestore serialization
            // This prevents the daysOfWeek from being serialized as a raw array string
            val habitData = mapOf(
                "id" to if (habit.id == 0L) "" else habit.id.toString(),
                "name" to habit.name,
                "description" to habit.description,
                "creationDate" to habit.creationDate,
                "currentStreak" to habit.currentStreak,
                "completionDatesJson" to habit.completionDatesJson,
                "uuid" to habit.uuid,
                "isArchived" to habit.isArchived,
                "position" to habit.position,
                "customOrderIndex" to habit.customOrderIndex,
                "color" to habit.color,
                "sectionId" to habit.sectionId,
                "type" to habit.type,
                "targetType" to habit.targetType,
                "targetValue" to habit.targetValue,
                "unit" to habit.unit,
                "frequencyType" to habit.frequencyType,
                "repeatsEvery" to habit.repeatsEvery,
                "daysOfWeek" to habit.daysOfWeek, // This stores the list of day numbers
                "dayOfMonth" to habit.dayOfMonth,
                "weekOfMonth" to habit.weekOfMonth,
                "dayOfWeekInMonth" to habit.dayOfWeekInMonth
            )

            // Add the habit to Firestore (auto-generate document ID)
            val documentRef = firestore
                .collection(USERS_COLLECTION)
                .document(userId)
                .collection(HABITS_COLLECTION)
                .add(habitData)
                .await()

            Log.d(TAG, "Habit inserted with ID: ${documentRef.id}")
        } catch (e: Exception) {
            Log.e(TAG, "Error inserting habit", e)
            throw e
        }
    }

    /**
     * Updates an existing habit for the current user.
     * FIXED: Find the document by UUID first, then update it to prevent creating duplicates.
     */
    suspend fun updateHabit(habit: Habit) {
        val userId = auth.currentUser?.uid
        if (userId == null) {
            Log.w(TAG, "User not authenticated, cannot update habit")
            throw IllegalStateException("User not authenticated")
        }

        try {
            // FIXED: Find the actual Firestore document ID by UUID
            val snapshot = firestore
                .collection(USERS_COLLECTION)
                .document(userId)
                .collection(HABITS_COLLECTION)
                .get()
                .await()

            var documentId: String? = null
            for (document in snapshot.documents) {
                val firestoreHabit = document.toObject(FirestoreHabit::class.java)
                if (firestoreHabit?.uuid == habit.uuid) {
                    documentId = document.id
                    break
                }
            }

            if (documentId == null) {
                Log.e(TAG, "Habit with UUID ${habit.uuid} not found for update")
                throw IllegalStateException("Habit not found for update")
            }

            // Convert habit to a Map to ensure proper Firestore serialization
            val habitData = mapOf(
                "id" to documentId, // Use the actual Firestore document ID
                "name" to habit.name,
                "description" to habit.description,
                "creationDate" to habit.creationDate,
                "currentStreak" to habit.currentStreak,
                "completionDatesJson" to habit.completionDatesJson,
                "uuid" to habit.uuid,
                "isArchived" to habit.isArchived,
                "position" to habit.position,
                "customOrderIndex" to habit.customOrderIndex,
                "color" to habit.color,
                "sectionId" to habit.sectionId,
                "type" to habit.type,
                "targetType" to habit.targetType,
                "targetValue" to habit.targetValue,
                "unit" to habit.unit,
                "frequencyType" to habit.frequencyType,
                "repeatsEvery" to habit.repeatsEvery,
                "daysOfWeek" to habit.daysOfWeek, // This stores the list of day numbers
                "dayOfMonth" to habit.dayOfMonth,
                "weekOfMonth" to habit.weekOfMonth,
                "dayOfWeekInMonth" to habit.dayOfWeekInMonth
            )

            // FIXED: Use the actual Firestore document ID for the update
            firestore
                .collection(USERS_COLLECTION)
                .document(userId)
                .collection(HABITS_COLLECTION)
                .document(documentId)
                .set(habitData)
                .await()

            Log.d(TAG, "Habit updated: ${habit.uuid} (document ID: $documentId)")
        } catch (e: Exception) {
            Log.e(TAG, "Error updating habit ${habit.uuid}", e)
            throw e
        }
    }

    /**
     * Updates custom order indices for multiple habits in a single batch operation.
     * This ensures atomic updates and better performance for reordering operations.
     */
    suspend fun updateCustomOrderIndices(habitOrderMap: Map<String, Int>): Boolean {
        val userId = auth.currentUser?.uid
        if (userId == null) {
            Log.w(TAG, "User not authenticated")
            return false
        }

        return try {
            // Get all habits to find their Firestore document IDs
            val snapshot = firestore
                .collection(USERS_COLLECTION)
                .document(userId)
                .collection(HABITS_COLLECTION)
                .get()
                .await()

            // Create a batch write
            val batch = firestore.batch()
            var updateCount = 0

            for (document in snapshot.documents) {
                val firestoreHabit = document.toObject(FirestoreHabit::class.java)
                firestoreHabit?.let { habit ->
                    val newOrderIndex = habitOrderMap[habit.uuid]
                    if (newOrderIndex != null) {
                        // Update the customOrderIndex field
                        val documentRef = firestore
                            .collection(USERS_COLLECTION)
                            .document(userId)
                            .collection(HABITS_COLLECTION)
                            .document(document.id)

                        batch.update(documentRef, "customOrderIndex", newOrderIndex)
                        updateCount++
                    }
                }
            }

            if (updateCount > 0) {
                batch.commit().await()
                Log.d(TAG, "Successfully updated custom order indices for $updateCount habits")
                true
            } else {
                Log.w(TAG, "No habits found to update custom order indices")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error updating custom order indices", e)
            false
        }
    }

    /**
     * Deletes a habit for the current user.
     */
    suspend fun deleteHabit(habit: Habit) {
        val userId = auth.currentUser?.uid
        if (userId == null) {
            Log.w(TAG, "User not authenticated, cannot delete habit")
            throw IllegalStateException("User not authenticated")
        }

        try {
            firestore
                .collection(USERS_COLLECTION)
                .document(userId)
                .collection(HABITS_COLLECTION)
                .document(habit.id.toString())
                .delete()
                .await()

            Log.d(TAG, "Habit deleted: ${habit.id}")
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting habit ${habit.id}", e)
            throw e
        }
    }
}
